# MonitorFilter 组件使用说明

## 概述

`MonitorFilter` 是一个统一的监控筛选组件，用于封装各个监控页面的筛选条件逻辑，提供一致的用户体验和代码复用。

## 特性

- 🎯 **统一接口**: 提供一致的筛选条件接口
- 🔧 **灵活配置**: 支持动态配置筛选字段
- ⏰ **时间范围**: 内置时间范围选择器
- 🔄 **自动重置**: 支持一键重置所有筛选条件
- 📱 **响应式**: 适配不同屏幕尺寸

## 基本用法

```vue
<template>
  <MonitorFilter
    v-model="filters"
    title="筛选条件"
    :loading="loading"
    :fields="filterFields"
    @filter-change="handleFilterChange"
    @time-range-change="handleTimeRangeChange"
    @reset="handleFilterReset"
  />
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import MonitorFilter from '@/components/monitor/MonitorFilter.vue'
import type { FilterConfig, TimeRangeData } from '@/types/monitor-filter'
import { FILTER_FIELD_PRESETS, createDefaultFilterConfig } from '@/types/monitor-filter'

// 筛选条件
const filters = reactive<FilterConfig>(
  createDefaultFilterConfig(['eventId', 'userUuid', 'pageUrl'])
)

// 筛选字段配置
const filterFields = FILTER_FIELD_PRESETS.ERROR_FIELDS

// 事件处理
function handleFilterChange() {
  // 处理筛选条件变化
  console.log('筛选条件变化:', filters)
}

function handleTimeRangeChange(timeRange: TimeRangeData) {
  // 处理时间范围变化
  console.log('时间范围变化:', timeRange)
}

function handleFilterReset() {
  // 处理重置筛选条件
  console.log('重置筛选条件')
}
</script>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `FilterConfig` | - | 筛选条件对象 |
| `title` | `string` | `'筛选条件'` | 筛选面板标题 |
| `loading` | `boolean` | `false` | 加载状态 |
| `fields` | `FilterField[]` | `[]` | 筛选字段配置 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `FilterConfig` | 筛选条件更新 |
| `filter-change` | - | 筛选条件变化 |
| `time-range-change` | `TimeRangeData` | 时间范围变化 |
| `reset` | - | 重置筛选条件 |

## 筛选字段配置

### FilterField 接口

```typescript
interface FilterField {
  key: string           // 字段键名
  label: string         // 显示标签
  type: 'input' | 'select'  // 字段类型
  placeholder?: string  // 占位符
  options?: Array<{     // 选择项（仅 select 类型）
    value: string
    label: string
  }>
  width?: string        // 字段宽度
}
```

### 预设字段配置

组件提供了预设的字段配置，可直接使用：

```typescript
import { FILTER_FIELD_PRESETS } from '@/types/monitor-filter'

// 错误监控字段
FILTER_FIELD_PRESETS.ERROR_FIELDS

// 性能监控字段
FILTER_FIELD_PRESETS.PERFORMANCE_FIELDS

// 页面访问监控字段
FILTER_FIELD_PRESETS.PAGE_VIEW_FIELDS

// 会话监控字段
FILTER_FIELD_PRESETS.SESSION_FIELDS
```

## 类型定义

### 筛选配置类型

```typescript
// 基础筛选配置
interface BaseFilterConfig {
  timeRange: string
  customStartTime: string
  customEndTime: string
}

// 错误监控筛选配置
interface ErrorFilterConfig extends BaseFilterConfig {
  eventId: string
  userUuid: string
  pageUrl: string
}

// 性能监控筛选配置
interface PerformanceFilterConfig extends BaseFilterConfig {
  metricType: string
  pageUrl: string
  userUuid: string
}

// 页面访问监控筛选配置
interface PageViewFilterConfig extends BaseFilterConfig {
  pageUrl: string
  userUuid: string
  visitType: string
}

// 会话监控筛选配置
interface SessionFilterConfig extends BaseFilterConfig {
  userUuid: string
  platform: string
  deviceType: string
}
```

## 工具函数

### createDefaultFilterConfig

创建默认筛选配置的工厂函数：

```typescript
import { createDefaultFilterConfig } from '@/types/monitor-filter'

// 创建包含指定字段的默认配置
const filters = createDefaultFilterConfig<ErrorFilterConfig>([
  'eventId', 'userUuid', 'pageUrl'
])
```

## 在现有页面中使用

各个监控页面已经更新为使用 `MonitorFilter` 组件：

- `ErrorMonitor.vue` - 错误监控
- `PerformanceMonitor.vue` - 性能监控  
- `PageViewMonitor.vue` - 页面访问监控
- `SessionMonitor.vue` - 会话监控

## 自定义筛选字段

如果需要自定义筛选字段，可以创建自己的字段配置：

```typescript
const customFields: FilterField[] = [
  {
    key: 'customField',
    label: '自定义字段',
    type: 'input',
    placeholder: '请输入...',
    width: '200px'
  },
  {
    key: 'customSelect',
    label: '自定义选择',
    type: 'select',
    placeholder: '请选择...',
    options: [
      { value: 'option1', label: '选项1' },
      { value: 'option2', label: '选项2' }
    ]
  }
]
```

## 注意事项

1. 确保 `v-model` 绑定的对象包含所有字段的键
2. 时间范围变化会自动触发，无需手动处理
3. 重置功能会将所有字段重置为默认值
4. 组件内部使用 `reactive` 进行响应式处理
